#!/usr/bin/env python3
"""
测试重构后的分层信息审查服务
验证新的参数结构和功能是否正常工作
"""

import asyncio
from typing import List
from drawing_service.stratification_review_service import StratificationReviewService, LogicResult
from models.section_models import DetectedSectionResult, SectionDetail, DrillHole, DepthElevationPair
from models.sheet_models import DetectedTableResult
from models.pdf_page import PdfPageType
from vision.core.data_types import DetectionResult


def create_mock_section_result() -> DetectedSectionResult:
    """创建模拟的剖面图结果"""
    # 创建深度-高程数据对
    depth_elevation_pairs = [
        DepthElevationPair(
            value="2.5-15.2",
            depth=2.5,
            elevation=15.2,
            x1=100, y1=100, x2=150, y2=120
        ),
        DepthElevationPair(
            value="5.0-12.7",
            depth=5.0,
            elevation=12.7,
            x1=100, y1=130, x2=150, y2=150
        )
    ]
    
    # 创建钻孔
    drill_hole = DrillHole(
        drill_name="ZK001",
        surface_elevation=17.7,
        depth_elevation_pairs=depth_elevation_pairs,
        x1=50, y1=50, x2=200, y2=200
    )
    
    # 创建剖面详情
    section_detail = SectionDetail(
        drill_holes=[drill_hole],
        x1=0, y1=0, x2=300, y2=300
    )
    
    # 创建剖面结果
    section_result = DetectedSectionResult(
        file_name="test_section.jpg",
        detect_type=PdfPageType.SECTION.desc,
        image_width=800,
        image_height=600,
        sections=[section_detail]
    )
    
    return section_result


def create_mock_table_result() -> DetectedTableResult:
    """创建模拟的表格结果（钻孔柱状图）"""
    # 创建模拟的OCR识别结果
    vision_results = [
        DetectionResult(
            original_text="ZK001钻孔柱状图",
            x1=10, y1=10, x2=200, y2=30
        ),
        DetectionResult(
            original_text="土层层号 层底深度 层底标高",
            x1=10, y1=50, x2=300, y2=70
        ),
        DetectionResult(
            original_text="1",
            x1=10, y1=80, x2=30, y2=100
        ),
        DetectionResult(
            original_text="2.5",
            x1=50, y1=80, x2=80, y2=100
        ),
        DetectionResult(
            original_text="15.2",
            x1=100, y1=80, x2=130, y2=100
        ),
        DetectionResult(
            original_text="2",
            x1=10, y1=110, x2=30, y2=130
        ),
        DetectionResult(
            original_text="5.0",
            x1=50, y1=110, x2=80, y2=130
        ),
        DetectionResult(
            original_text="12.7",
            x1=100, y1=110, x2=130, y2=130
        )
    ]
    
    table_result = DetectedTableResult(
        file_name="test_table.jpg",
        detect_type=PdfPageType.DRILL_BAR_CHART.desc,
        image_width=800,
        image_height=600,
        vision_results=vision_results
    )
    
    return table_result


async def test_stratification_review():
    """测试分层信息审查服务"""
    print("开始测试重构后的分层信息审查服务...")
    
    # 创建服务实例
    service = StratificationReviewService()
    
    # 创建测试数据
    geo_report: List[LogicResult] = [
        create_mock_section_result(),
        create_mock_table_result()
    ]
    
    print(f"创建了 {len(geo_report)} 个测试数据项")
    print(f"数据类型: {[item.detect_type for item in geo_report]}")
    
    try:
        # 执行审查
        result = await service.review_stratification_info(geo_report)
        
        print("\n=== 审查结果 ===")
        print(f"钻孔数量: {result['summary']['total_drill_holes']}")
        print(f"总层级数: {result['summary']['total_layers']}")
        print(f"对比次数: {result['summary']['total_comparisons']}")
        print(f"不一致项: {result['summary']['total_inconsistencies']}")
        print(f"一致性率: {result['summary']['consistency_rate']}%")
        
        print("\n=== 钻孔数据 ===")
        for drill_id, drill_data_list in result['drill_holes'].items():
            print(f"钻孔 {drill_id}: {len(drill_data_list)} 个数据点")
            for data in drill_data_list:
                print(f"  - 第{data['layer_index']}层 ({data['source_type']}): "
                      f"深度={data['depth']}m, 标高={data['elevation']}m")
        
        print("\n=== 数据源统计 ===")
        for source_type, count in result['summary']['source_type_statistics'].items():
            print(f"{source_type}: {count} 个数据点")
        
        if result['inconsistencies']:
            print("\n=== 不一致项 ===")
            for inconsistency in result['inconsistencies']:
                print(f"- {inconsistency['description']}")
        
        print("\n=== 建议 ===")
        for recommendation in result['summary']['recommendations']:
            print(f"- {recommendation}")
        
        print("\n✅ 测试成功完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_stratification_review())
    exit(0 if success else 1)
