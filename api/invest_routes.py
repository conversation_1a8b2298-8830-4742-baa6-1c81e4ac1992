from collections import defaultdict
from typing import List, Union, Dict, Any, Optional, Annotated
from fastapi import APIRouter, Body
from pydantic import Field

from api.base import ApiResponse
from models.legend_models import DetectedLegendResult
from models.misaligned_table_models import MisalignedTableResult, TableInfo
from models.pdf_page import PdfPageType
from models.plane_models import DetectedPlainResult
from models.section_models import DetectedSectionResult, DrillHole
from models.section_models import DetectedSectionResult, DrillSpacingComparison
from models.sheet_models import DetectedTableResult
from drawing_service.stratification_review_service import StratificationReviewService
from loguru import logger
from vision.ocr_engine.external.roman import validate_labels, repair_labels

router = APIRouter(prefix="/invest", tags=["审查报告相关 API"])


LogicResult = Annotated[
    Union[
        DetectedLegendResult,
        MisalignedTableResult,
        DetectedPlainResult,
        DetectedSectionResult,
        DetectedTableResult,
    ],
    Field(discriminator="detect_type"),   # 关键！
]


def compare_drill_spacings(section_results: List[DetectedSectionResult], 
                          plane_results: List[DetectedPlainResult]) -> List[DrillSpacingComparison]:
    """
    对比剖面图和平面图中的钻孔间距
    
    Args:
        section_results: 剖面图结果列表
        plane_results: 平面图结果列表
        
    Returns:
        钻孔间距对比结果列表
    """
    comparisons = []
    
    # 构建平面图钻孔间距的映射 {(drill1_name, drill2_name): spacing_value}
    plane_spacing_map = {}
    for plane_result in plane_results:
        for spacing in plane_result.drill_spacings:
            if spacing.drill1.binding_info and spacing.drill2.binding_info:
                drill1_name = spacing.drill1.binding_info.drill_number
                drill2_name = spacing.drill2.binding_info.drill_number
                # 确保钻孔对的一致性（按字母顺序排列）
                drill_pair = tuple(sorted([drill1_name, drill2_name]))
                plane_spacing_map[drill_pair] = spacing.plane_spacing
    
    # 遍历剖面图结果，与平面图进行对比
    for section_result in section_results:
        for spacing_with_drill in section_result.spacing_with_drill_holes:
            drill1_name = spacing_with_drill.drill_hole1.drill_name
            drill2_name = spacing_with_drill.drill_hole2.drill_name
            
            # 确保钻孔对的一致性（按字母顺序排列）
            drill_pair = tuple(sorted([drill1_name, drill2_name]))
            section_spacing = spacing_with_drill.section_spacing.value
            
            # 检查平面图中是否存在对应的钻孔对
            if drill_pair in plane_spacing_map:
                plane_spacing = plane_spacing_map[drill_pair]
                difference = abs(section_spacing - plane_spacing)
                has_issue = difference != 0
                
                comparison = DrillSpacingComparison(
                    drill_hole1_name=drill1_name,
                    drill_hole2_name=drill2_name,
                    section_spacing=section_spacing,
                    plane_spacing=plane_spacing,
                    difference=difference,
                    has_issue=has_issue
                )
                comparisons.append(comparison)
            else:
                comparison = DrillSpacingComparison(
                    drill_hole1_name=drill1_name,
                    drill_hole2_name=drill2_name,
                    section_spacing=section_spacing,
                    plane_spacing=None,
                    difference=None,
                    has_issue=True,
                    comments="未找到对应的平面图钻孔对"
                )
                comparisons.append(comparison)
    
    return comparisons


@router.post("/invest_report")
async def invest_report(
    layout: List[LogicResult] = Body(...),
    geo_report: List[LogicResult] = Body(..., alias="geoReport"),
):
    """
    对逻辑结果进行修补
    """
    logic_result_map = defaultdict(list)
    for logic_result in geo_report:
        logic_result_map[logic_result.detect_type].append(logic_result)

    # 审查钻口信息

    # 审查勘探孔间距
    section_results = logic_result_map[PdfPageType.SECTION.desc]
    plane_results = logic_result_map[PdfPageType.FLOOR_PLAN.desc]
    spacing_reviews = compare_drill_spacings(section_results, plane_results)
    logger.debug(spacing_reviews)

    # 审查分层信息
    stratification_review_service = StratificationReviewService()
    stratification_review_result = await stratification_review_service.review_stratification_info(geo_report)

    # 返回数据结构待定
    return ApiResponse.success_response(data={
        "stratification_review": stratification_review_result
        # "original_results": logic_results
    })
