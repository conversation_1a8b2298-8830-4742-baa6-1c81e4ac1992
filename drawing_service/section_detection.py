import json
import os
from typing import List, <PERSON>, <PERSON><PERSON>, Dict, Any, Optional

import re
import cv2
import numpy as np

from exceptiongroup import catch
from loguru import logger
from networkx.algorithms.operators.binary import difference
from openai import OpenAI
from collections import Counter

from aikit.core.manager import ModelManager
from aikit.core.registry import registry, register_model_instance
from aikit.core.data_types import ModelType
from aikit.providers.openai_compatible.config import OpenAIConfig
from aikit.providers.auto_discovery import auto_register_ai_models
from models.base import VisionDetectedResult
from models.pdf_page import PdfPageType
from utils.pdf import PdfTextItem
from vision.core import DetectionResult, ImageHandler
from vision.core.data_types import BoundingBox, OcrItem, Difficult
from vision.core.utils import crop_bboxes, non_maximum_suppression
from vision.obj_det import YOLOModelManager, YoloModelType
from vision.obj_det.yolo_holder import SectionElemType
from vision.ocr_engine.engine import Ocr<PERSON><PERSON><PERSON>, OcrModelType
from drawing_service.base import DetectorHel<PERSON>, VisionComputer
from models.section_models import DetectedSectionResult, SectionDetail, SectionNumber, DrillHole, Spacing, SamplePoint, \
    DepthElevationPair, Ruler, RulerItem, SpacingWithDrillHole

SPACING_TEXT = "间距"


class SectionVisionComputer(VisionComputer, DetectorHelper):
    """剖面区域检测器"""

    def __init__(
            self,
            ocr_model: OcrModelType = OcrModelType.RAPID,
            confidence_threshold: float = 0.5,
            iou_threshold: float = 0.45,
            pair_nms_threshold: float = 0.1
    ):
        super().__init__(PdfPageType.SECTION, ocr_model, confidence_threshold, iou_threshold)
        self.pair_nms_threshold = pair_nms_threshold
        self.all_verticical_lines = List[List[int]]

    def handle_vision_detection(self, image: np.ndarray):
        self.add_image_predict(YoloModelType.SECTION_FULL, image)
        self.add_large_image_predict(YoloModelType.SECTION_SOIL_SAMPLE_DETECTION, image)
        self.add_large_image_predict(YoloModelType.SECTION_PAIR_DETECTION, image, tile_size=(896, 896), overlap_ratio=0.5, merge_nms_threshold=0.1)

    def handle_ocr_detection(self, image: np.ndarray):
        
        self._detect_vertical_lines_full_image(image)

        # 剖面编号、钻口、高程对识别
        self.merge_ocr(self.batch_ocr_processor.process(
            self._filter_in_class(
                self.detections,
                SectionElemType.SECTION_NUMBER.value,
                SectionElemType.DRILL_HOLE.value,
                SectionElemType.DEPTH_ELEVATION_PAIR.value
            ), image, self.ocr_engine.recognize
        ))

    def handle_logic_compute(self, image, vision_result: VisionDetectedResult) -> DetectedSectionResult:
        # 剖面区域
        detected_sections: List[DetectionResult] = self.filter_by_class(SectionElemType.SECTION.value)
        if len(detected_sections) == 0:
            logger.warning("未检测到任何剖面区域")
            return DetectedSectionResult(
                file_name=vision_result.file_name,
                detect_type=self.page_type.desc,
                image_width=vision_result.image_width,
                image_height=vision_result.image_height,
                sections=[]
            )
        detected_sections = sorted(detected_sections, key=lambda x: (x.y_min, x.x_min))
        logger.info(f"检测到 {len(detected_sections)} 个剖面区域")

        # 2、处理剖面区域检测结果
        sections = []
        # 找到所有间距分组
        spacing_groups = self._find_spacing_group()
        if len(spacing_groups) == len(detected_sections):
            for detection, spacings in zip(detected_sections, spacing_groups):
                section = SectionDetail(
                    **detection.model_dump(),
                    section_number=self._find_section_number(image, detection),
                    ruler=self._find_ruler(image, detection),
                    drill_holes=[],
                    spacings=spacings,
                )
                self._fill_drill_hole(image, section, detection)
                sections.append(section)
        else:
            logger.warning(f"间距数量({len(spacing_groups)})与剖面区域数量({len(detected_sections)})不匹配，可能存在错误")
            for detection in detected_sections:
                section = SectionDetail(
                    **detection.model_dump(),
                    section_number=self._find_section_number(image, detection),
                    ruler=self._find_ruler(image, detection),
                    drill_holes=[],
                    spacings=self._find_spacings(image, detection),
                )
                self._fill_drill_hole(image, section, detection)
                sections.append(section)
        spacings_with_drill_holes = self._extract_spacings_with_drill_hole(sections)
        return DetectedSectionResult(
            file_name=vision_result.file_name,
            detect_type=self.page_type.desc,
            image_width=vision_result.image_width,
            image_height=vision_result.image_height,
            sections=sections,
            spacing_with_drill_holes=spacings_with_drill_holes
        )

    @property
    def _detected_details(self):
        return self.filter_in_class(
            SectionElemType.SECTION_NUMBER.value,
            SectionElemType.SCALE.value,
            SectionElemType.DRILL_HOLE.value
        )

    def _check_drill_hole_spacing_conflicts(self, drill_holes: List[DetectionResult], tolerance: float = 200.0) -> None:
        """
        检测钻孔之间的距离冲突并标记为 difficult

        :param drill_holes: 已按 x 坐标升序排序的钻孔列表
        :param tolerance: 距离容差阈值（像素）
        """
        if len(drill_holes) < 2:
            return

        logger.info(f"开始检测 {len(drill_holes)} 个钻孔的间距冲突，容差阈值: {tolerance}px")
        conflict_count = 0

        for i in range(1, len(drill_holes)):
            current_hole = drill_holes[i]
            previous_hole = drill_holes[i - 1]

            # 计算当前钻孔与前一个钻孔中心点之间的距离
            current_center = current_hole.center
            previous_center = previous_hole.center
            distance = ((current_center[0] - previous_center[0]) ** 2 +
                       (current_center[1] - previous_center[1]) ** 2) ** 0.5

            # 计算前一个钻孔bbox宽度的一半
            previous_bbox_half_width = (previous_hole.x2 - previous_hole.x1) / 2

            # 判断是否存在冲突：距离 < tolerance + 前一个钻孔bbox宽度的一半
            conflict_threshold = tolerance + previous_bbox_half_width

            if distance < conflict_threshold:
                conflict_count += 1

                # 创建或更新 difficult 属性
                if current_hole.difficult is None:
                    current_hole.difficult = Difficult()

                conflict_msg = (f"钻孔间距冲突: 与前一钻孔距离{distance:.1f}px < "
                              f"阈值{conflict_threshold:.1f}px (容差{tolerance}px + "
                              f"前钻孔半宽{previous_bbox_half_width:.1f}px)")

                current_hole.difficult.notes = conflict_msg
                current_hole.difficult.origin_text = f"钻孔位置: {current_center}"
                current_hole.difficult.optimized_text = f"冲突检测结果: 距离过近"

                logger.warning(f"检测到钻孔间距冲突: 钻孔{i} 与钻孔{i-1} 距离{distance:.1f}px < 阈值{conflict_threshold:.1f}px")

        if conflict_count > 0:
            logger.warning(f"共检测到 {conflict_count} 个钻孔存在间距冲突")
        else:
            logger.info("未检测到钻孔间距冲突")

    def _find_section_number(self, image, detection: DetectionResult) -> Optional[SectionNumber]:
        """
        在剖面检测结果中查找对应的剖面编号
        :param image: 原始图像
        :param detection: 剖面检测结果
        """

        section_number = self._find_class_in_bbox(detection, self._detected_details, class_name=SectionElemType.SECTION_NUMBER.value)
        if len(section_number) == 0:
            logger.warning(f"未找到剖面区域的剖面编号")
            return None
        elif len(section_number) > 1:
            logger.warning(f"找到多个剖面编号，可能存在误检，将使用第一个检测到的剖面编号")
        section_number = section_number[0]

        text = OcrEngine.find_best_text(section_number, self.roi_ocr_result)
        if not text:
            text = self.ocr_engine.find_best_text(section_number, self.ocr_result)

        return SectionNumber(**section_number.model_dump()).model_copy(update=dict(
            original_text=text
        ))

    def _find_ruler(self, image, detection) -> Optional[Ruler]:
        # 找到剖面区域的所有标尺元素
        ruler_items = self._find_class_in_bbox(detection, self._detected_details, class_name=SectionElemType.SCALE.value)
        if not ruler_items:
            logger.warning("未找到剖面区域的标尺信息")
            return None
        elif len(ruler_items) > 1:
            logger.warning("找到多个标尺信息，可能存在误检，将使用第一个检测到的标尺信息")
        ruler_item = ruler_items[0]
        # 识别文本
        roi = ImageHandler.crop_bbox(image, ruler_item)
        ocr_result = self.ocr_engine.recognize(roi)
        texts = ocr_result.items

        if not texts:
            logger.warning("OCR 未识别到任何文本")
            return None

        # 过滤文本中的空格
        for item in texts:
            item.text = re.sub(r'\s+', '', item.text)  # 去掉所有空格
            item.text = re.sub(r'_','',item.text) # 去掉错误识别到的 _
        texts = sorted(texts, key=lambda text: text.y1)
        #
        # values = []
        # for item in texts:
        #     text = re.sub(r'\s+', '', item.text)  # 去掉所有空格
        #     text = re.sub(r'_', '', text)  # 去掉错误识别到的 _
        #     try:
        #         values.append(float(text))
        #     except ValueError:
        #         continue  # 不是数字就跳过
        #
        # diffs = [values[i+1]-values[i] for i in range(len(values)-1)]
        #
        # counter = Counter(diffs)
        # most_common_diff ,count= counter.most_common(1)[0]
        #
        # print("出现次数最多的差值:", most_common_diff, "出现次数:", count)
        #
        #

        # 填充标尺基本信息
        ruler_items = []
        for item in texts:
            try:
                item = item.map_to_original(ruler_item)
                value = float(item.text)
                ruler_items.append(RulerItem(
                    **item.model_dump(),
                    value=value,
                    y=item.y2
                ).model_copy(update=dict(
                    class_name=SectionElemType.SCALE,
                    original_text=item.text,
                )))
            except ValueError as e:
                logger.warning(f"解析标尺文本失败: {item.text}, 错误: {e}")
                continue
        # 计算标尺每像素对应的实际长度
        if len(ruler_items) < 2:
            logger.warning("标尺信息不足，无法计算每像素对应的实际长度，同时将无法计算采样点深度信息")
            return None
        item1, item2 = ruler_items[:2]
        value_per_pixel = -(item1.value - item2.value) / (item1.y - item2.y)

        return Ruler(
            **ruler_item.model_dump(),
            value_per_pixel=value_per_pixel,
            ruler_items=ruler_items,
        ).model_copy(update=dict(
            original_text=None,
        ))

    def _fill_drill_hole(self, image, section_detail: SectionDetail, detect_section: DetectionResult) -> None:
        """
        查找钻孔信息
        :return: 钻孔列表
        """
        # 找到当前剖面所有钻孔
        detect_drill_holes = self._find_class_in_bbox(detect_section, self._detected_details, class_name=SectionElemType.DRILL_HOLE.value)
        if not detect_drill_holes:
            logger.warning("未找到任何钻孔信息")
            return

        # 钻口按照 x 坐标升序
        detect_drill_holes = sorted(detect_drill_holes, key=lambda x: x.x1)

        # 检测钻孔间距冲突并标记为 difficult
        self._check_drill_hole_spacing_conflicts(detect_drill_holes)

        # 校验间距数量与钻孔数量是否匹配
        spacings = section_detail.spacings
        if len(spacings) != len(detect_drill_holes) - 1:
            logger.warning("间距数量与钻孔数量不匹配，为钻口分配的间距可能存在错误！")

        # 填装数据
        for idx, hole in enumerate(detect_drill_holes):
            text = OcrEngine.find_best_text(hole, self.roi_ocr_result)
            # 原本单阶段提取深度高程对
            if self.pdf_texts:
                ruler = section_detail.ruler
                y_min, y_max = ruler.y_min, ruler.y_max + 100
                depth_elevation_items = self._find_item_in_bbox(detect_section, self.pdf_texts)
                depth_elevation_items = self._find_item_in_y_range(y_min, y_max, depth_elevation_items)
            else:
                depth_elevation_items = self._find_pair_detections_in_section(detect_section)
            # 第一阶段：使用pair模型检测深度高程对的边界框位置
            # pair_detections = self._find_pair_detections_in_section(detect_section)
            info = self._parse_drill_hole_text(text)
            elevation = float(info.get("elevation", "0"))
            hole_name = info.get("hole_id", "")
            logger.info(f"正在采集钻孔 {hole_name} 相关信息")
            drill_hole = DrillHole(
                **hole.model_dump(),
                drill_name=hole_name,
                surface_elevation=elevation,
                # depth_elevation_pairs=self._extract_depth_elevation(hole, depth_elevation_items),
                depth_elevation_pairs=self._extract_depth_elevation_from_pairs(image, hole, depth_elevation_items, section_detail.ruler, elevation),
                x_coordinate=hole.xyxy[0],
                is_terminal_hole=idx == len(detect_drill_holes) - 1,
                samples=self._find_sample_points(section_detail.ruler, elevation, detect_section, hole)
            ).model_copy(update=dict(
                original_text=text,
            ))
            if idx < len(detect_drill_holes) - 1:
                self._assign_spacing(idx, drill_hole, spacings)
            section_detail.drill_holes.append(drill_hole)

    def _assign_spacing(self, idx: int, drill_hole: DrillHole, spacings: List[Spacing]) -> None:
        """
        为钻孔分配间距信息
        :param idx: 钻孔索引
        :param drill_hole: 钻孔对象
        :param spacings: 间距列表
        """
        if idx < len(spacings):
            spacing = spacings[idx]
            drill_hole.assigned_spacing = spacing
            logger.info(f"为钻孔 {drill_hole.drill_name} 分配间距: {spacing.value}m, 原文='{spacing.original_text}'")
        else:
            logger.warning(f"钻孔 {drill_hole.drill_name} 没有对应的间距信息")

    def _find_spacing_group(self) -> List[List[Spacing]]:
        """
        查找所有间距组
        :return: 间距组列表
        """
        spacing_groups: List[List[Spacing]] = []

        # 找到“间距”文本
        spacing_text_items: List[OcrItem] = []
        for item in self.ocr_result.items:
            if item.text and SPACING_TEXT in item.text:
                spacing_text_items.append(item)
        if len(spacing_text_items) == 0:
            logger.warning("未找到任何间距信息")
            return []

        # 找到同行其他间距信息
        spacing_lines_dict = {}
        for item in spacing_text_items:
            lines = OcrEngine.find_items_in_y_range(item.y_min, item.y_max, self.ocr_result, 0.5)
            for l in lines:
                key = (l.x_min, l.y_min, l.x_max, l.y_max, l.text)  # 唯一标识
                spacing_lines_dict[key] = l

        spacing_lines = list(spacing_lines_dict.values())

        # 按 y 坐标分组
        sorted_items = sorted(spacing_lines, key=lambda i: (i.y_min, i.x_min))

        line_groups: List[List[OcrItem]] = []
        current_line: List[OcrItem] = []

        for item in sorted_items:
            if not current_line:
                current_line.append(item)
                continue
            # 只要与当前行中任意一个元素 y 轴重合比例 >= 0.6 就认为是同一行
            if any(item.y_overlap_ratio(existing) >= 0.6 for existing in current_line):
                current_line.append(item)
            else:
                # 新的一行
                line_groups.append(sorted(current_line, key=lambda i: i.x_min))
                current_line = [item]

        if current_line:
            line_groups.append(sorted(current_line, key=lambda i: i.x_min))

        # 每一行内部根据 SPACING_TEXT 进行二次分组
        for line in line_groups:
            # 找到所有 SPACING_TEXT 的位置（按 x 排序）
            spacing_positions = [idx for idx, it in enumerate(line) if it.text and SPACING_TEXT in it.text]
            if not spacing_positions:
                continue

            # 遍历 SPACING_TEXT，找出它后面的所有元素作为一组
            for i, pos in enumerate(spacing_positions):
                start_idx = pos + 1
                end_idx = spacing_positions[i + 1] if i + 1 < len(spacing_positions) else len(line)
                group_items = line[start_idx:end_idx]
                # 构建 Spacing 对象
                group_items = [self._build_spacing(item) for item in group_items if item.text and SPACING_TEXT not in item.text]
                if group_items:
                    spacing_groups.append(group_items)

        return spacing_groups

    def _build_spacing(self, spacing_item: OcrItem) -> Spacing:
        """
        构建间距对象
        :param spacing_item: 间距文本项
        :return: Spacing 对象
        """
        try:
            # 提取间距数值
            match = re.search(r'(\d+\.?\d*)\s*', spacing_item.text)
            if match:
                spacing_value = float(OcrEngine.fix_ocr_float_separation(match.group(1)))
                return Spacing(
                    **spacing_item.model_dump(),
                    value=spacing_value
                ).model_copy(update=dict(
                    class_name=SectionElemType.SPACING.SPACING,
                    original_text=spacing_item.text,
                ))
            else:
                logger.warning(f"未能从间距文本 '{spacing_item.text}' 中提取数值")
                return Spacing(**spacing_item.model_dump())
        except ValueError as e:
            logger.error(f"解析间距数值失败: {spacing_item.text}, 错误: {e}")
            return Spacing(**spacing_item.model_dump())

    def _extract_depth_elevation(self, detect_hole: DetectionResult, text_items: List[PdfTextItem]) -> List[
        DepthElevationPair]:
        """
        从整个图像中提取"xxx(xxx)"格式的深度-高程数据对
        支持 OCR 常见空格/分隔符错误
        """
        tolerance = 200  # 容差范围
        x_min, x_max = detect_hole.xyxy[0], detect_hole.xyxy[2] + tolerance
        line = self._find_vertical_line_under_drill(x_min, x_max)
        # x_center = (x_min + x_max) / 2
        tmp_center = (x_min + x_max) / 2
        x_center = (line[0] if line else (x_min + x_max) / 2) - 50
        logger.debug(f"提取深度-高程对DEBUG: 钻孔中心线 x={x_center}, 对照中心线 {tmp_center}, x_min={x_min}, x_max={x_max}, line={line}")
        # x_center = tmp_center
        text_items = [
            item for item in text_items if x_center <= item.x1 and abs(item.x1 - x_center) <= 100
        ]
        # 按 y 升序
        text_items = sorted(text_items, key=lambda i: i.y_min)

        pairs = []

        # 原始正则，支持负数和小数
        pattern = r'(-?\d+(?:\.\d+)?)\s*\(\s*(-?\d+(?:\.\d+)?)\s*\)'

        def normalize_number_text(text: str) -> str:
            """
            修复 OCR 数字常见错误：
            1. 小数点与数字之间的空格 → 去掉
            2. 负号与数字之间的空格 → 去掉
            3. 去掉括号和数字之间的多余空格
            """
            # 20. 00 → 20.00
            text = re.sub(r'(\d)\s*\.\s*(\d)', r'\1.\2', text)
            # - 15.53 → -15.53
            text = re.sub(r'-\s*(\d)', r'-\1', text)
            # ( -15.53 ) → (-15.53)
            text = re.sub(r'\(\s*-\s*(\d)', r'(-\1', text)
            return text

        for item in text_items:
            if not item.text:
                continue

            # 修复 OCR 数字空格问题
            fixed_text = normalize_number_text(item.text)

            # 查找所有匹配的深度-高程对
            matches = re.findall(pattern, fixed_text)

            for match in matches:
                try:
                    depth = float(match[0])
                    elevation = float(match[1])

                    pair = DepthElevationPair(
                        **item.model_dump(),
                        value=f"{depth}({elevation})",
                        depth=depth,
                        elevation=elevation,
                    ).model_copy(update=dict(
                        class_name=SectionElemType.DEPTH_ELEVATION_PAIR,
                        confidence=1,
                        original_text=item.text,
                    ))
                    pairs.append(pair)
                    logger.info(
                        f"提取深度-高程对: 深度={depth}m, 高程={elevation}m, 原文='{pair.original_text}' 修正后='{fixed_text}'")

                except (ValueError, IndexError) as e:
                    logger.warning(f"解析深度-高程对失败: {item.text}, 错误: {e}")
                    continue

        if not pairs:
            logger.warning("未提取到任何深度-高程数据对")
        else:
            logger.info(f"共提取到 {len(pairs)} 个的深度-高程数据对")

        return pairs

    def _find_sample_points(self, ruler: Ruler, elevation: float,
                            detect_section: DetectionResult, detect_hole: DetectionResult) -> List[SamplePoint]:
        """
        查找钻孔下的采样点
        :param ruler: 剖面标尺
        :param elevation: 钻孔孔口标高
        :param detect_section: 检测的剖面
        :param detect_hole: 检测的钻孔
        :return: 采样点列表
        """
        sample_points = []

        # 在当前剖面区域内查找采样点
        detected_samples = self.filter_in_class(
            SectionElemType.SOIL_SAMPLE_CIRCLE.value,
            SectionElemType.SOIL_SAMPLE_TRIANGLE.value
        )
        detect_sample_points = self._find_item_in_bbox(detect_section, detected_samples)
        # 查找钻孔下的采样点
        tolerance = 20  # 容差范围，允许采样点在钻孔区域外一定范围内
        x_min, x_max = detect_hole.xyxy[0] - tolerance, detect_hole.xyxy[2] + tolerance
        detect_sample_points = self._find_item_in_x_range(x_min, x_max, detect_sample_points)

        if not detect_sample_points:
            return []

        # 按 y 轴升序
        detect_sample_points = sorted(detect_sample_points, key=lambda x: x.y1)
        for sample_point in detect_sample_points:
            # 计算采样点的深度和高程
            depth, sample_elevation = self._calculate_sample_depth(ruler, elevation, sample_point)
            sample_points.append(SamplePoint(
                **sample_point.model_dump(),
                depth=depth,
                elevation=sample_elevation,
                sample_type=sample_point.class_name,
            ))
        return sample_points

    def _calculate_sample_depth(self, ruler: Ruler, elevation: float, sp_bbox: BoundingBox) -> Tuple[float, float]:
        """
        计算采样点的深度和高程
        :param ruler: 剖面标尺
        :param elevation: 钻孔孔口标高
        :param sp_bbox: 采样点的边界框
        :return: (深度, 高程)
        """
        if not ruler or not ruler.value_per_pixel:
            logger.warning("无法计算采样点深度，标尺信息不足")
            return 0.0, elevation

        if elevation == 0:
            logger.warning("钻孔孔口标高为0，为异常高度，可能导致采样点深度计算不准确")

        # 计算采样点的 Y 坐标相对于标尺的偏移量
        y_offset = (sp_bbox.center[1]) - ruler.ruler_items[0].y

        # 计算采样点高程
        sample_elevation = ruler.ruler_items[0].value - y_offset * ruler.value_per_pixel

        # 计算采样点深度（单位：米）
        depth = elevation - sample_elevation

        return depth, sample_elevation

    def _parse_drill_hole_text(self, text: str) -> Dict[str, Any]:
        """
        解析钻孔OCR文字，提取钻孔编号和高程
        OCR结果格式:
            第一行：编号（数字、字母、短横线）
            第二行：高程（浮点数）
        """
        result: Dict[str, Any] = {}
        try:
            text = text.strip()
            parts = [p.strip() for p in text.split("\n") if p.strip()]

            if not parts:
                return result

            hole_id_raw = parts[0]
            # 只保留字母、数字、短横线
            hole_id = re.sub(r'[^A-Za-z0-9-]', '', hole_id_raw).upper()
            if hole_id:
                result["hole_id"] = hole_id

            if len(parts) > 1:
                elevation_raw = parts[1]

                # 修复 4 . 96 → 4.96
                elevation_raw = re.sub(r'(\d)\s*\.\s*(\d+)', r'\1.\2', elevation_raw)

                # 修复 4 96 → 4.96（仅当第二个是2位数字时）
                elevation_raw = re.sub(r'(\d)\s+(\d{2})\b', r'\1.\2', elevation_raw)

                # 提取浮点数
                match = re.search(r'(\d+\.?\d*)', elevation_raw)
                if match:
                    try:
                        result["elevation"] = float(match.group(1))
                    except ValueError:
                        pass

        except Exception as e:
            logger.error(f"解析钻孔文字失败: {e}")

        return result

    def _find_pair_detections_in_section(self, detect_section: BoundingBox) -> List[BoundingBox]:
        """
        在剖面区域内查找pair模型检测到的深度高程对边界框，并应用NMS过滤重叠框
        :param detect_section: 剖面检测结果
        :return: 经过NMS处理的pair检测结果列表
        """
        if self.pdf_texts:
            pairs_in_section = self._find_item_in_bbox(detect_section, self.pdf_texts)
        else:
            # 获取所有pair检测结果
            pair_detections = self.filter_by_class(SectionElemType.DEPTH_ELEVATION_PAIR.value)

            # 过滤出在当前剖面区域内的pair检测结果
            pairs_in_section = self._find_class_in_bbox(detect_section, pair_detections, class_name=SectionElemType.DEPTH_ELEVATION_PAIR.value)

        return pairs_in_section

    def _extract_depth_elevation_from_pairs(
            self,
            image: np.ndarray,
            detect_hole: BoundingBox,
            pair_detections: List[BoundingBox],
            ruler: Optional[Ruler] = None,
            surface_elevation: float = 0.0
    ) -> List[DepthElevationPair]:
        """
        基于pair模型检测结果提取深度-高程数据对（两阶段处理）
        第一阶段：pair模型已经检测出边界框位置
        第二阶段：对每个边界框区域单独进行OCR识别

        :param image: 原始图像
        :param detect_hole: 钻孔检测结果
        :param pair_detections: pair模型检测到的边界框列表
        :return: 深度高程对列表
        """
        tolerance = 100  # 容差范围
        x_min, x_max = detect_hole.xyxy[0] - tolerance, detect_hole.xyxy[2] + tolerance

        # 按照bbox边缘分配
        # 过滤出在钻孔附近的pair检测结果
        relevant_pairs = [pair for pair in pair_detections if x_min <= pair.x_min and x_max >= pair.x_max]
        relevant_pairs.sort(key=lambda pair: pair.y1)

        # 原始正则，支持负数和小数
        pattern = r'(-?\d+(?:\.\d+)?)\s*\(\s*(-?\d+(?:\.\d+)?)\s*\)'

        def normalize_number_text(text: str) -> str:
            """修复 OCR 数字常见错误"""
            text = re.sub(r'\s+', '', text)  # 去掉空白
            text = re.sub(r'\).+', ')', text)  # 去掉 ) 后面的字符
            text = re.sub(r'~', '-', text)  # ~ -> -
            text = re.sub(r'[/\\\[\]]', '', text)  # 去掉 / \ [ ]
            text = re.sub(r'[，,*:]', '.', text)  # , ， * : -> .
            return text

        # 计算参考值
        reference_pairs_result = self._calculate_reference_depth_elevation_pairs(
            detect_hole, relevant_pairs, ruler, surface_elevation
        )

        # ----------- 收集OCR文本 ----------
        ocr_texts: List[str] = []
        for pair_detection in relevant_pairs:
            try:
                rois, _ = crop_bboxes(image, [pair_detection.xyxy])
                if not rois:
                    logger.warning(f"无法裁剪pair区域: {pair_detection.xyxy}")
                    continue
                ocr_result = OcrEngine.find_best_text(pair_detection, self.roi_ocr_result)
                fixed_text = normalize_number_text(ocr_result)
                matches = re.findall(pattern, fixed_text)
                for match in matches:
                    logger.info(f"使用正则修复的结果：高程：{match[0]}，深度：{match[1]}")
                ocr_texts.append(ocr_result)
            except Exception as e:
                logger.error(f"OCR提取出错: {e}")
                continue
        for ocr_text in ocr_texts:
            logger.info(f"原始OCR结果：{ocr_text}")


        # ----------- 调用Qwen优化 ----------
        optimized_pairs = self.optimize_with_qwen(ocr_texts, reference_pairs_result)

        # ----------- 校验 & 构造结果 ----------
        pairs: List[DepthElevationPair] = []
        temp_depth = float('-inf')
        temp_elevation = float('inf')

        # IQR异常值检测范围
        preview_depths = [p["depth"] for p in optimized_pairs if "depth" in p]
        preview_elevations = [p["elevation"] for p in optimized_pairs if "elevation" in p]

        if preview_depths and preview_elevations:
            q1_depth, q3_depth = np.percentile(preview_depths, [25, 75])
            iqr_depth = q3_depth - q1_depth
            lower_depth = q1_depth - 1.5 * iqr_depth
            upper_depth = q3_depth + 1.5 * iqr_depth

            q1_elev, q3_elev = np.percentile(preview_elevations, [25, 75])
            iqr_elev = q3_elev - q1_elev
            lower_elev = q1_elev - 1.5 * iqr_elev
            upper_elev = q3_elev + 1.5 * iqr_elev
        else:
            lower_depth, upper_depth = float('-inf'), float('inf')
            lower_elev, upper_elev = float('-inf'), float('inf')

        monotonic_count = 0
        outlier_count = 0
        over_lap_count = 0
        difficult_count = 0

        for idx, pair_detection in enumerate(relevant_pairs):
            try:
                if idx >= len(optimized_pairs):
                    continue
                depth = float(optimized_pairs[idx]["depth"])
                elevation = float(optimized_pairs[idx]["elevation"])

                notes = []
                monotonic_ok = True
                outlier_ok = True
                overlap_ok = True

                # 单调性校验
                if depth < temp_depth or elevation > temp_elevation:
                    msg = f"单调性异常: 深度={depth}m, 高程={elevation}m"
                    logger.warning(msg)
                    notes.append(msg)
                    monotonic_ok = False
                    monotonic_count += 1

                # 异常值校验
                if depth < lower_depth or depth > upper_depth or elevation < lower_elev or elevation > upper_elev:
                    msg = f"数据合理性异常: 深度={depth}m, 高程={elevation}m (超出IQR范围)"
                    logger.warning(msg)
                    notes.append(msg)
                    outlier_ok = False
                    outlier_count += 1

                if idx > 0 and  relevant_pairs[idx - 1].y2 - relevant_pairs[idx].y1 > 20:
                    msg = f"数据异常: 数据重叠、识别效果差: 下边界：{relevant_pairs[idx].y2}  上边界：{relevant_pairs[idx + 1].y1 }"
                    logger.warning(msg)
                    notes.append(msg)
                    overlap_ok = False
                    overlap_ok += 1

                if monotonic_ok and outlier_ok and overlap_ok:
                    temp_depth = depth
                    temp_elevation = elevation
                else:
                    difficult_count += 1
                    if pair_detection.difficult is None:
                        pair_detection.difficult = Difficult()
                    pair_detection.difficult.origin_text = ocr_texts[idx] if idx < len(ocr_texts) else ""
                    pair_detection.difficult.optimized_text = f"{depth}({elevation})"
                    pair_detection.difficult.notes = "; ".join(notes) or "校验失败"

                # 保存结果
                pair = DepthElevationPair(
                    **pair_detection.model_dump(),
                    value=f"{depth}({elevation})",
                    depth=depth,
                    elevation=elevation,
                ).model_copy(update=dict(
                    class_name=SectionElemType.DEPTH_ELEVATION_PAIR,
                    confidence=min(pair_detection.confidence, 1),
                    original_text=ocr_texts[idx] if idx < len(ocr_texts) else "",
                ))
                pairs.append(pair)

                logger.info(
                    f"提取深度-高程对: 深度={depth}m, 高程={elevation}m, 校验结果={'正常' if (monotonic_ok and outlier_ok) else '异常'}"
                )
            except Exception as e:
                logger.warning(f"处理优化结果失败: {e}")
                continue

        # ----------- 输出统计 ----------
        total = len(pairs)
        stats = {
            "总点数": total,
            "单调性异常数": monotonic_count,
            "异常值异常数": outlier_count,
            "数据重叠异常数": over_lap_count,
            "difficult数": difficult_count,
        }
        logger.info(f"深度-高程对校验统计: {stats}")

        if not pairs:
            logger.warning("未提取到任何深度-高程数据对")
        else:
            logger.info(f"共提取到 {total} 个深度-高程数据对")

        return pairs

    def _detect_vertical_lines_full_image(self, image: np.ndarray) -> List[List[int]]:
        """
        在整个图像范围内检测竖直线
        优化后的竖线检测方法，不限制Y坐标范围

        :param image: 输入图像
        :return: 检测到的竖直线列表，格式为[[x1, y1, x2, y2], ...]
        """
        height, width = image.shape[:2]
        logger.info(f"在整个图像范围内检测竖线，图像尺寸: {image.shape}")

        result_image = image.copy()

        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 高斯模糊去噪
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Canny边缘检测
        edges = cv2.Canny(blurred, 80, 120, apertureSize=3)

        # 概率霍夫变换检测直线 - 调整参数以检测更多竖线
        lines = cv2.HoughLinesP(edges, 1, np.pi / 180, threshold=100,
                                minLineLength=100, maxLineGap=8)

        vertical_lines = []

        if lines is not None:
            logger.info(f"初步检测到 {len(lines)} 条线段")

            for line in lines:
                x1, y1, x2, y2 = line[0]
                # 判断是否为竖直线（更宽松的条件）
                if abs(x2 - x1) < 30:
                    vertical_lines.append([x1, y1, x2, y2])

            logger.info(f"检测到 {len(vertical_lines)} 条竖直线")

            # 合并相近的竖直线
            merged_lines = self._merge_vertical_lines(vertical_lines, distance_threshold=20)
            logger.info(f"合并后剩下 {len(merged_lines)} 条竖直线")

            self.all_verticical_lines = merged_lines

            return merged_lines
        else:
            logger.warning("未检测到任何线段")
            return []

    def _merge_vertical_lines(self, lines: List[List[int]], distance_threshold: int = 15) -> List[List[int]]:
        """
        合并距离很近的竖直线
        从recognize_line.py提取的合并方法

        :param lines: 竖直线列表
        :param distance_threshold: 判断两条线是否相近的x坐标距离阈值
        :return: 合并后的竖直线列表
        """
        if len(lines) == 0:
            return []

        # 按x坐标排序
        sorted_lines = sorted(lines, key=lambda x: x[0])

        merged_lines = []
        current_group = [sorted_lines[0]]

        for i in range(1, len(sorted_lines)):
            current_line = sorted_lines[i]
            last_line_in_group = current_group[-1]

            # 计算两条线x坐标的距离
            distance = abs(current_line[0] - last_line_in_group[0])

            if distance <= distance_threshold:
                # 距离在阈值内，加入当前组
                current_group.append(current_line)
            else:
                # 距离超出阈值，处理当前组并开始新组
                if current_group:
                    merged_line = self._merge_line_group(current_group)
                    merged_lines.append(merged_line)
                current_group = [current_line]

        # 处理最后一组
        if current_group:
            merged_line = self._merge_line_group(current_group)
            merged_lines.append(merged_line)

        return merged_lines

    def _merge_line_group(self, group: List[List[int]]) -> List[int]:
        """
        将一组相近的竖直线合并为一条线
        从recognize_line.py提取的合并方法

        :param group: 相近的竖直线组
        :return: 合并后的竖直线 [x, y1, x, y2]
        """
        x_values = [line[0] for line in group]
        avg_x = int(np.mean(x_values))

        # 找到y的最小值和最大值
        all_y = []
        for line in group:
            all_y.extend([line[1], line[3]])

        min_y = min(all_y)
        max_y = max(all_y)

        return [avg_x, min_y, avg_x, max_y]

    def _filter_vertical_lines_enhanced(self, vertical_lines: List[List[int]],
                                       spacing_text_items: List, y_min: int, y_max: int) -> List[List[int]]:
        """
        竖线过滤方法

        :param vertical_lines: 检测到的所有竖线
        :param spacing_text_items: 间距文本项列表
        :param y_min: 间距区域最小Y坐标
        :param y_max: 间距区域最大Y坐标
        :return: 过滤后的竖线列表
        """
        if not vertical_lines:
            return []

        logger.info(f"开始过滤竖线，原始数量: {len(vertical_lines)}")

        # 1. 计算每条竖线的长度，按长度排序
        lines_with_length = []
        for line in vertical_lines:
            x1, y1, x2, y2 = line
            length = abs(y2 - y1)
            lines_with_length.append((line, length))

        # 按长度降序排序
        lines_with_length.sort(key=lambda x: x[1], reverse=True)

        # 2. 去掉最长的两条竖线
        if len(lines_with_length) > 2:
            filtered_by_length = [item[0] for item in lines_with_length[2:]]
            logger.info(f"去掉最长的2条竖线后剩余: {len(filtered_by_length)}条")
        else:
            logger.warning("竖线数量不足，无法去掉最长的两条")
            return []

        # 3. 去掉位于间距文本bbox范围内的竖线（带容差）
        x_tolerance = 200  # 容差像素
        filtered_lines = []

        for line in filtered_by_length:
            x1, y1, x2, y2 = line
            line_x = (x1 + x2) // 2  # 竖线的x坐标

            # 检查是否在任何间距文本的x范围内
            should_filter = False
            for spacing_item in spacing_text_items:
                text_x_min = spacing_item.x_min - x_tolerance
                text_x_max = spacing_item.x_max + x_tolerance

                if text_x_min <= line_x <= text_x_max:
                    should_filter = True
                    logger.debug(f"竖线x={line_x}在间距文本范围[{text_x_min}-{text_x_max}]内，将被过滤")
                    break

            if not should_filter:
                filtered_lines.append(line)

        logger.info(f"去掉间距文本范围内的竖线后剩余: {len(filtered_lines)}条")

        # 4. 按x坐标排序
        filtered_lines.sort(key=lambda line: line[0])

        return filtered_lines

    def _find_spacings_with_vertical_lines(self, image: np.ndarray, detect_section: DetectionResult,
                                                   spacing_text_items: List, y_min: int, y_max: int) -> List[Spacing]:
        """
        基于竖线检测的间距识别方法
        合并了竖线检测、过滤和OCR识别的完整流程

        :param image: 原始图像
        :param detect_section: 剖面检测结果
        :param spacing_text_items: 间距文本项列表
        :param y_min: 间距表格区域的最小Y坐标
        :param y_max: 间距表格区域的最大Y坐标
        :return: 间距列表
        """
        logger.info(f"开始基于竖线检测的间距识别，Y范围: {y_min}-{y_max}")

        # 1. 在整个图像范围内检测竖直线
        all_vertical_lines = self._detect_vertical_lines_full_image(image)

        if len(all_vertical_lines) < 4:
            logger.warning(f"检测到的竖线数量不足({len(all_vertical_lines)}条)，无法进行间距识别")
            return []

        # 2. 过滤与间距无关的竖线
        filtered_lines = self._filter_vertical_lines_enhanced(all_vertical_lines, spacing_text_items, y_min, y_max)

        if len(filtered_lines) < 2:
            logger.warning(f"过滤后的竖线数量不足({len(filtered_lines)}条)，无法进行间距识别")
            return []

        logger.info(f"过滤后保留 {len(filtered_lines)} 条竖线用于间距识别")

        # 3. 创建OCR识别区域并进行识别
        spacings = []
        for i in range(len(filtered_lines) - 1):
            left_line = filtered_lines[i]
            right_line = filtered_lines[i + 1]

            # 定义OCR区域的边界
            x_min = left_line[0]  # 左竖线的x坐标
            x_max = right_line[0]  # 右竖线的x坐标
            ocr_y_min = y_min
            ocr_y_max = y_max

            # 确保区域有效
            if x_max <= x_min or ocr_y_max <= ocr_y_min:
                logger.warning(f"无效的OCR区域: x({x_min}-{x_max}), y({ocr_y_min}-{ocr_y_max})")
                continue

            # 4. 裁剪OCR区域并进行识别
            roi = image[ocr_y_min:ocr_y_max, x_min:x_max]

            if roi.size == 0:
                logger.warning(f"OCR区域为空: x({x_min}-{x_max}), y({ocr_y_min}-{ocr_y_max})")
                continue

            logger.info(f"OCR区域 {i+1}: x({x_min}-{x_max}), y({ocr_y_min}-{ocr_y_max}), 尺寸: {roi.shape}")

            # 4. OCR识别
            ocr_result = OcrEngine.get_instance().recognize(roi)
            if not ocr_result.items:
                logger.warning(f"OCR区域 {i + 1} 未识别到任何文本")
                continue

            # === 新增：合并多个 OCR 结果 ===
            if len(ocr_result.items) == 1:
                merged_items = ocr_result.items
            else:
                texts = [it.text for it in ocr_result.items if it.text]
                if not texts:
                    continue

                merged_text = "".join(texts)
                x1 = x_min
                y1 = ocr_y_min
                x2 = x_max
                y2 = ocr_y_max
                confidence = sum(it.confidence for it in ocr_result.items) / len(ocr_result.items)

                merged_items = [OcrItem(
                    text=merged_text,
                    confidence=confidence,
                    x1=x1, y1=y1, x2=x2, y2=y2
                )]

            # 5. 构建 Spacing
            for ocr_item in merged_items:
                if not ocr_item.text or SPACING_TEXT in ocr_item.text:
                    continue

                spacing = self._build_spacing(ocr_item)
                if spacing:
                    spacings.append(spacing)
                    logger.info(f"识别到间距值: {spacing.value}m, 原文: '{spacing.original_text}'")

        logger.info(f"检测共识别到 {len(spacings)} 个间距值")
        return spacings


    def optimize_with_qwen(self, ocr_texts: List[str], reference_pairs: List[str]) -> List[dict]:
        """
        调用 Qwen 模型优化 OCR 文本，返回标准化的深度-高程对
        :param ocr_texts: OCR 原始识别文本数组
        :param reference_pairs: 参考估算的深度高程对数组
        :return: List[{"depth": float, "elevation": float}]
        """
        prompt = f"""
    你是一个OCR文本修复助手。现在有两组数据：
    1. OCR识别结果（主要依据）
    2. 参考估算结果（辅助修正）

    任务：输出修复后的深度-高程对，格式严格为 Depth(Elevation)，并保证为浮点数。
    只输出JSON数组，每个元素包含 "depth" 和 "elevation"。
    深度都是正数，高程可以是正数也可以是负数。
    深度为递增的，高程为递减的。
    优化后的结果必须为数字
    返回的结果数量要与非空的OCR的识别结果数量完全一致，数量方面要更多的参考OCR识别结果，而不是参考估算结果。

    OCR结果: {ocr_texts}
    参考值: {reference_pairs}

    输出示例：
    [
      {{"depth": 6.00, "elevation": -1.59}},
      {{"depth": 9.46, "elevation": 12.20}}
    ]
        """

        client = OpenAI(
            api_key=os.getenv("DASHSCOPE_API_KEY"),
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )

        response = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {"role": "system", "content": "You are a precise data extraction assistant."},
                {"role": "user", "content": prompt},
            ],
            temperature=0.2,
        )

        content = response.choices[0].message.content.strip()

        # --- 清理 Markdown 代码块标记 ---
        if content.startswith("```"):
            content = re.sub(r"^```[a-zA-Z]*", "", content).strip()
            content = re.sub(r"```$", "", content).strip()

        try:
            return json.loads(content)
        except json.JSONDecodeError:
            logger.error(f"模型输出无法解析为 JSON: {content}")
            return []

    def _find_spacings(self, image: np.ndarray, detect_section: DetectionResult) -> List[Spacing]:
        """
        基于竖线检测的间距识别方法，自动从OCR结果中获取间距区域

        :param image: 原始图像
        :param detect_section: 剖面检测结果
        :return: 间距列表
        """
        # 1. 从OCR结果中查找"间距"文本项，自动计算Y坐标范围
        spacing_text_items = []
        for item in self.ocr_result.items:
            if item.text and SPACING_TEXT in item.text:
                spacing_text_items.append(item)

        if not spacing_text_items:
            logger.warning("未找到间距文本，无法进行间距识别")
            return []

        # 2. 基于找到的间距文本计算Y坐标范围
        y_tolerance = 30  # 容差像素
        all_y_mins = [item.y_min for item in spacing_text_items]
        all_y_maxs = [item.y_max for item in spacing_text_items]

        y_min = max(0, min(all_y_mins) - y_tolerance)
        y_max = max(all_y_maxs) + y_tolerance

        logger.info(f"基于{len(spacing_text_items)}个间距文本项自动计算Y坐标范围: {y_min}-{y_max}")

        # 3. 使用基于竖线检测的策略
        return self._find_spacings_with_vertical_lines(image, detect_section,
                                                              spacing_text_items, y_min, y_max)

    def _find_vertical_line_under_drill(self, x_min: int, x_max: int) -> List[int]:
        """
            基于竖直线检测的间距边界识别策略
            params:
                image: 输入图像
            return: 识别到的间距边界框列表
        """
        # 在整个图像范围内检测竖直线
        all_vertical_lines = self.all_verticical_lines
        for line in all_vertical_lines:
            if line[0] >= x_min and line[2] <= x_max:
                return line
        return None

    def _calculate_reference_depth_elevation_pairs(self, detect_hole: BoundingBox,
                                                 relevant_pairs: List[BoundingBox],
                                                 ruler: Optional[Ruler] = None,
                                                 surface_elevation: float = 0.0) -> List[str]:
        """
        基于已有的钻孔数据和间距信息计算逻辑深度高程对作为参考数据
        参考_calculate_sample_depth方法的实现模式

        :param detect_hole: 钻孔检测结果
        :param pair_detections: pair模型检测到的边界框列表
        :param ruler: 标尺信息（可选）
        :param surface_elevation: 钻孔孔口标高（可选）
        :return: 深度高程对字符串列表，格式为["深度(高程)", ...]
        """
        reference_pairs = []

        try:
            # 生成参考深度高程对
            for i, pair_bbox in enumerate(relevant_pairs):
                try:

                    # 基于位置计算参考深度和高程，参考_calculate_sample_depth的算法逻辑
                    if ruler and ruler.value_per_pixel and ruler.ruler_items:
                        # 使用标尺信息计算精确的深度高程（与_calculate_sample_depth相同的算法）
                        y_offset = pair_bbox.center[1] - ruler.ruler_items[0].y
                        calculated_elevation = ruler.ruler_items[0].value - y_offset * ruler.value_per_pixel
                        calculated_depth = surface_elevation - calculated_elevation

                        # 确保深度为正值
                        calculated_depth = max(0.0, calculated_depth)

                        logger.debug(f"使用标尺计算: Y偏移={y_offset:.1f}px, "
                                   f"每像素值={ruler.value_per_pixel:.4f}, "
                                   f"标尺基准值={ruler.ruler_items[0].value:.2f}")
                    else:
                        # 没有标尺信息时，使用简化的估算方法
                        # 基于Y坐标位置估算深度（假设每100像素约1米深度）
                        pixel_depth = pair_bbox.center[1] - detect_hole.y1
                        estimated_depth = max(0.0, pixel_depth / 100.0)  # 简化的像素到米的转换
                        calculated_depth = estimated_depth
                        calculated_elevation = surface_elevation - calculated_depth

                        logger.debug(f"使用估算方法: 像素深度={pixel_depth:.1f}px, "
                                   f"估算深度={estimated_depth:.2f}m")

                    # 格式化为标准的深度(高程)格式
                    reference_pair = f"{calculated_depth:.2f}({calculated_elevation:.2f})"
                    reference_pairs.append(reference_pair)

                    logger.debug(f"生成参考深度高程对 {i+1}: {reference_pair}")

                except Exception as e:
                    logger.warning(f"计算第{i+1}个参考深度高程对时出错: {e}")
                    continue

            logger.info(f"成功生成 {len(reference_pairs)} 个参考深度高程对")

        except Exception as e:
            logger.error(f"计算参考深度高程对时发生错误: {e}")

        return reference_pairs
        
    def _extract_spacings_with_drill_hole(self, sections: List[SectionDetail]):
        """
            从剖面数据中提取间距和钻孔组合信息
            :param sections: 剖面数据列表
            :return: 钻孔间距组合列表
        """
        if sections is None or len(sections) < 1:
            logger.warning("无法提取间距信息，剖面数据为空")
            return []

        spacing_with_drill_holes = []

        # 遍历钻孔和间距，构建 SpacingWithDrillHole 对象
        for section in sections:
            spacings = section.spacings
            drill_holes = section.drill_holes
            if len(spacings) >= len(drill_holes):
                continue
            for i in range(len(spacings)):
                drill_hole1 = drill_holes[i]
                spacing = spacings[i]
                drill_hole2 = drill_holes[i + 1]

                # 创建 SpacingWithDrillHole 对象
                spacing_with_drill_hole = SpacingWithDrillHole(
                    section_spacing=spacing,
                    drill_hole1=drill_hole1,
                    drill_hole2=drill_hole2
                )

                spacing_with_drill_holes.append(spacing_with_drill_hole)
                logger.info(f"构建钻孔间距组合: {drill_hole1.drill_name} - {spacing.value}m - {drill_hole2.drill_name}")

        logger.info(f"共构建 {len(spacing_with_drill_holes)} 个钻孔间距组合")
        return spacing_with_drill_holes



if __name__ == '__main__':
    from pathlib import Path

    image_path = r"E:\CODE_MGR\01-SHProject\AlgoApi\page_26.png"
    file_name = Path(image_path).name
    detector = SectionVisionComputer()
    result = detector.detect(file_name, image_path)
    detector.vision_detect(file_name, image_path)
    detector.visualization(
        class_names=[
            SectionElemType.DEPTH_ELEVATION_PAIR.value
        ],
        save_path='test.png'
    )

    # print(result.model_dump_json(indent=2))
