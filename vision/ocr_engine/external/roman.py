import re
from typing import Optional
from typing import List, Tuple
from collections import defaultdict

# 基础映射表（可扩展）
OCR_CORRECTIONS = {
    "一": "I", "1": "I", "l": "I", "|": "I", "！": "I",
    "二": "II", "2": "II",
    "三": "III", "3": "III",
    "四": "IV", "4": "IV",
    "五": "V", "5": "V",
    "六": "VI", "6": "VI",
    "七": "VII", "7": "VII",
    "八": "VIII", "8": "VIII",
    "九": "IX", "9": "IX",
    "十": "X", "0": "X",
}
CHINESE_NUMERALS = set("一二三四五六七八九十")

# 罗马数字 <-> 整数映射
ROMAN_MAP = [
    ("M", 1000), ("CM", 900), ("D", 500), ("CD", 400),
    ("C", 100), ("XC", 90), ("L", 50), ("XL", 40),
    ("X", 10), ("IX", 9), ("V", 5), ("IV", 4), ("I", 1)
]


def roman_to_int(s: str) -> int:
    i, num = 0, 0
    while i < len(s):
        for sym, val in ROMAN_MAP:
            if s[i:i + len(sym)] == sym:
                num += val
                i += len(sym)
                break
    return num


def int_to_roman(num: int) -> str:
    res = ""
    for sym, val in ROMAN_MAP:
        while num >= val:
            res += sym
            num -= val
    return res


def normalize_text(text: str) -> str:
    # 替换 OCR 错误
    for k, v in OCR_CORRECTIONS.items():
        text = text.replace(k, v)
    # 去掉全角符号
    text = text.replace("'", "'").replace("'", "'")
    return text


def parse_label(text: str):
    """解析成整数编号"""
    text = normalize_text(text)
    # 提取左/右部分
    parts = re.split(r"[-—]", text)
    if len(parts) >= 2:
        left = re.sub(r"[^IVXLCDM]", "", parts[0])
        return roman_to_int(left) if left else None
    return None


def extract_section_number(text: str) -> tuple[Optional[str], Optional[str], bool]:
    """
    提取剖面号的主要部分和类型
    返回：(清理后的文本, 类型标识, 是否带引号)
    类型标识: 'roman' - 罗马数字, 'alpha_num' - 字母数字组合, 'num' - 纯数字
    """
    if text is None:
        return None, None, False

    # 清理文本
    text = str(text).strip()
    text = re.sub(r'\s+', '', text)  # 移除所有空白字符
    text = text.replace('\n', '')  # 移除换行符
    has_quote = any(q in text for q in ["'", "'", "′"])

    # 移除引号和分隔符
    text = re.sub(r"['′']", "", text)
    text = re.sub(r"[-—]+", "", text)

    # 判断类型
    if re.match(r'^[IVXLCDM]+$', text):
        return text, 'roman', has_quote
    elif re.match(r'^[A-Za-z]\d+$', text):
        return text, 'alpha_num', has_quote
    elif re.match(r'^\d+$', text) or text in OCR_CORRECTIONS:
        return text, 'num', has_quote

    return None, None, has_quote


def parse_complex_label(text: str) -> tuple[Optional[str], Optional[int]]:
    """
    解析复杂标签，返回(标准化文本, 数值)
    """
    if text is None:
        return None, None

    # 分割可能的多行内容
    # 修复: 使用更清晰的正则表达式来分割
    parts = re.split(r'-+|\n+', text)  # 改用单独的分隔符，用 | 来表示或关系
    main_part = parts[0] if parts else text

    cleaned_text, type_id, has_quote = extract_section_number(main_part)
    if cleaned_text is None:
        return None, None

    if type_id == 'roman':
        # 已经是罗马数字
        return cleaned_text, roman_to_int(cleaned_text)
    elif type_id == 'alpha_num':
        # 字母数字组合（如A1）
        letter = cleaned_text[0]
        num = int(cleaned_text[1:])
        return cleaned_text, num
    elif type_id == 'num':
        # 数字（可能是中文或阿拉伯数字）
        if cleaned_text in OCR_CORRECTIONS:
            cleaned_text = OCR_CORRECTIONS[cleaned_text]
            return cleaned_text, roman_to_int(cleaned_text)
        try:
            return cleaned_text, int(cleaned_text)
        except ValueError:
            return None, None

    return None, None


def format_label(text: str, num: int, pattern: str = None) -> str:
    """
    根据模式格式化标签
    """
    if pattern and 'A' in pattern:
        # 字母数字格式
        letter = re.match(r'([A-Za-z])', pattern).group(1)
        return f"{letter}{num}-{letter}{num}'"
    elif re.match(r'^[IVXLCDM]+$', text):
        # 罗马数字格式
        return f"{text}-{text}'"
    else:
        # 数字格式
        return f"{num}-{num}'"



def identify_label_group(text: str) -> str:
    """
    识别标签所属的组
    返回:
        'roman' - 罗马数字组 (包含罗马数字和中文数字)
        'num' - 数字组
        'alpha_num' - 字母数字组
    """
    if text is None:
        return None

    # 分割并获取主要部分
    parts = re.split(r'-+|\n+', text)
    main_part = parts[0] if parts else text

    # 清理文本
    cleaned = re.sub(r"['′']", "", main_part)
    cleaned = re.sub(r'\s+', '', cleaned)

    # 识别组别
    if re.match(r'^[IVXLCDM]+$', cleaned):  # 罗马数字
        return 'roman'
    elif all(ch in CHINESE_NUMERALS for ch in cleaned):  # 汉字数字
        return 'roman'
    elif re.match(r'^[A-Za-z]\d+$', cleaned):  # 字母+数字
        return 'alpha_num'
    elif re.match(r'^\d+$', cleaned):  # 纯数字
        return 'num'

    return None

def group_the_labels(labels: List[str]) -> dict:
    """
    将标签按类型分组
    """
    groups = defaultdict(list)
    for i, label in enumerate(labels):
        if label is None:
            continue
        group_type = identify_label_group(label)
        if group_type:
            groups[group_type].append((i, label))  # 保存原始索引和标签
    return groups


def validate_group(labels: List[Tuple[int, str]], max_error_ratio: float = 0.2) -> bool:
    """
    验证单个组内的标签序列
    """
    if not labels:
        return True

    nums = []
    for _, label in labels:
        _, num = parse_complex_label(label)
        if num is not None:
            nums.append(num)

    if not nums:
        return False

    # 检查组内递增规律
    errors = 0
    for i in range(1, len(nums)):
        if nums[i] <= nums[i - 1]:
            errors += 1

    error_ratio = errors / max(1, len(nums) - 1)
    return error_ratio <= max_error_ratio


def validate_labels(labels: List[str], max_error_ratio: float = 0.2) -> bool:
    """
    校验 labels 是否在各自的组内满足递增规律
    :param labels: 输入的编号列表
    :param max_error_ratio: 最大允许的错误比例 (默认20%)
    :return: (bool, dict) - (是否验证通过, 详细分组信息)
    """
    # 分组
    groups = group_the_labels(labels)
    if not groups:
        return False

    # 验证每个组
    group_results = {}
    for group_type, group_labels in groups.items():
        is_valid = validate_group(group_labels, max_error_ratio)
        group_results[group_type] = {
            'valid': is_valid,
            'labels': [label for _, label in group_labels],
            'indices': [idx for idx, _ in group_labels]
        }

    # 如果所有组都有效，则整体有效
    return all(result['valid'] for result in group_results.values())


def normalize_text(text: str) -> str:
    """
    标准化文本，处理 OCR 错误和全角符号
    增加空值处理
    """
    if not text:  # 处理 None 或空字符串情况
        return ""

    text = str(text)  # 确保输入是字符串
    # 替换 OCR 错误
    for k, v in OCR_CORRECTIONS.items():
        text = text.replace(k, v)
    # 去掉全角符号
    text = text.replace("'", "'").replace("'", "'")
    return text


def repair_labels(labels: List[str]) -> List[str]:
    """
    修复并标准化标签列表，保持分组规律
    每个分组（罗马数字、纯数字、字母数字组合）内部保持递增规律
    特别处理OCR错误导致的数字跳跃问题
    """
    if not labels:
        return []

    # 分组
    groups = group_the_labels(labels)
    if not groups:
        return labels

    # 创建结果列表，初始化为原始标签
    repaired = list(labels)

    # 为每个组单独修复
    for group_type, group_labels in groups.items():
        # 按组内索引顺序处理
        sorted_group = sorted(group_labels, key=lambda x: x[0])
        
        # 第一遍：收集所有解析出的数字
        parsed_nums = []
        for idx, label in sorted_group:
            lines = label.split('\n')
            main_text = lines[0]  # 使用第一行作为主要文本
            text, num = parse_complex_label(main_text)
            parsed_nums.append((idx, label, num))

        # 第二遍：检测并修复OCR错误
        corrected_nums = []
        expected_num = 1
        
        # 先分析整个序列，找出可能的序列分割点
        sequence_breaks = []
        for i in range(1, len(parsed_nums)):
            prev_num = parsed_nums[i-1][2]
            curr_num = parsed_nums[i][2]
            if prev_num is not None and curr_num is not None:
                # 如果数字跳跃超过5，可能是新序列的开始
                if curr_num - prev_num > 5:
                    sequence_breaks.append(i)
        
        # 特殊处理：如果最后一个数字明显大于前面的连续序列，也认为是新序列
        if len(parsed_nums) > 1:
            last_idx = len(parsed_nums) - 1
            last_num = parsed_nums[last_idx][2]
            if last_num is not None and last_num > 10:
                # 如果最后一个数字大于10，且前面有连续的1-9序列，则认为是新序列
                sequence_breaks.append(last_idx)
        
        for i, (idx, label, num) in enumerate(parsed_nums):
            # 检查是否是新序列的开始
            is_new_sequence = i in sequence_breaks
            if num is None:
                # 如果无法解析，使用期望的数字
                corrected_num = expected_num
            elif is_new_sequence:
                # 如果是新序列的开始，保持原值并重置期望数字
                corrected_num = num
                expected_num = num  # 重置期望数字
            elif num == expected_num:
                # 数字正确
                corrected_num = num
            elif num > expected_num and num - expected_num <= 3:
                # 可能是OCR错误，检查是否是常见的OCR错误模式
                # 例如：6被识别成9，8被识别成9等
                if num == 9 and expected_num == 6:
                    # 6被识别成9的常见错误
                    corrected_num = 6
                elif num == 9 and expected_num == 8:
                    # 8被识别成9的常见错误
                    corrected_num = 8
                elif num == 0 and expected_num == 6:
                    # 6被识别成0的常见错误
                    corrected_num = 6
                else:
                    # 其他情况，使用期望的数字
                    corrected_num = expected_num
            elif num > expected_num and num - expected_num > 3:
                # 数字跳跃很大，使用期望的数字
                corrected_num = expected_num
            else:
                # 数字小于期望值，使用期望的数字
                corrected_num = expected_num

            corrected_nums.append((idx, label, corrected_num))
            expected_num = corrected_num + 1

        # 第三遍：格式化修复后的标签
        for idx, label, num in corrected_nums:
            # 根据组类型格式化标签
            if group_type == 'roman':
                roman = int_to_roman(num)
                repaired[idx] = f"{roman}-{roman}'"
            elif group_type == 'num':
                repaired[idx] = f"{num}-{num}'"
            elif group_type == 'alpha_num':
                # 保持原有字母，只更新数字部分
                letter = re.match(r'([A-Za-z])', label).group(1) if re.match(r'([A-Za-z])', label) else 'A'
                repaired[idx] = f"{letter}{num}-{letter}{num}'"

    return repaired


if __name__ == '__main__':
    # 测试用户提供的示例数据
    data = ["A1-A1'", "A2-A2'", "A3-A3'", "A4-A4'", "A5-A5'", "A6 --- A6'", "A7 --- A7'", "A8 --- A8'", "A9 --- A9'", '1\n1', '2', "3 \n3'\n---", "4 --- 4'", "5 --- 5'", '9 --- 9', "7 --- 7'", "8\n8'", "9\n9'", None, "11\n11'"]
    print("输入数据:", data)
    result = repair_labels(data)
    print("修复结果:", result)

